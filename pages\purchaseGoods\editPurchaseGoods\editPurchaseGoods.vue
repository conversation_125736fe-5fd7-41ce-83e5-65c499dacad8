<template>
  <view class="container">
    <!-- <u-loading-page :loading="isLoading" loading-text="正在加载中..." bg-color="#e8e8e8" /> -->
    <scroll-view scroll-y class="content">
      <!-- 基本信息 -->
      <basicInfo :basicInfo="purchaseGoodsInfo" :isDisabled="prohibitModification" type="purchaseGoods" ref="basicInfo" @update:basicInfo="updatePurchaseGoodsInfo" />

      <!-- 商品清单 -->
      <merch-bill :items="purchaseGoodsInfo.items" type="purchaseGoods" :prohibit-modification="prohibitModification"
        :purchase-order-name="purchaseGoodsInfo.purchase_order_name" @open-product-select="addGoods"
        @add-association-goods="addAssociationGoods" @open-product-details="openProductDetails"
        @amount-change="handleAmountChange" />

      <!-- 结算账户 -->
      <view class="info">
        <view class="info_title">
          <text>结算账户</text>
        </view>
        <view class="form">
          <u--form labelPosition="left">
            <u-form-item :label-width="'180rpx'" label="结算账户" borderBottom :required="purchaseGoodsInfo.pay_amount > 0">
              <u-row>
                <u-col :span="purchaseGoodsInfo.payment_method_name && !prohibitModification ? 10.5 : 12">
                  <u--input v-model="purchaseGoodsInfo.payment_method_name" border="none" placeholder="请选择结算账户"
                    inputAlign="right" disabled disabledColor="#fff" @tap="openAccountSelector"></u--input>
                </u-col>
                <u-col span="0.5"></u-col>
                <u-col span="1">
                  <view @click="removePayAccount" v-if="!prohibitModification">
                    <i-close-one theme="filled" size="20" fill="#d13b3b" v-if="purchaseGoodsInfo.payment_method_name" />
                  </view>
                </u-col>
              </u-row>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="扣除预付款" borderBottom>
              <u--input v-model="purchaseGoodsInfo.deduct_deposit" border="none" placeholder="0" inputAlign="right"
                disabled disabledColor="color: #fff"></u--input>
            </u-form-item>
            <u-form-item :label-width="'180rpx'" label="本次付款" borderBottom>
              <u--input v-model="purchaseGoodsInfo.pay_amount" border="none" placeholder="0" inputAlign="right"
                @change="changePayAmount" :disabled="prohibitModification" disabledColor="color: #fff"></u--input>
            </u-form-item>
            <!-- 欠款和付款需要监听修改 -->
            <u-form-item :label-width="'180rpx'" label="本次欠款" borderBottom>
              <u--input v-model="purchaseGoodsInfo.debt" border="none" placeholder="0" inputAlign="right"
                @change="changeDebt" :disabled="prohibitModification" disabledColor="color: #fff"></u--input>
            </u-form-item>
          </u--form>
        </view>
      </view>

      <!-- 其他费用 -->
      <view class="info">
        <view class="info_title">
          <text>其他费用</text>
        </view>
        <view class="form">
          <view class="otherExpenses" @click="openOtherCosts">
            <text class="total-row-title">其他费用</text>
            <view class="other-expenses-row">
              <u--input v-model="purchaseGoodsInfo.allocated_pay_amount" border="none" placeholder="0"
                inputAlign="right" disabled disabledColor="#fff"></u--input>
              <view class="icon-group">
                <view @click.stop="removeOtherCosts" v-if="purchaseGoodsInfo.allocated_pay_amount">
                  <i-close-one theme="filled" size="20" fill="#d13b3b" />
                </view>
                <view>
                  <i-right theme="filled" size="20" fill="#333" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注 -->
      <view class="info">
        <view class="info_title">
          <text>备注</text>
        </view>
        <view class="remark">
          <u--textarea v-model="purchaseGoodsInfo.remark" placeholder="请输入备注" autoHeight border="none"
            :disabled="prohibitModification" disabledColor="color: #fff"></u--textarea>
        </view>
      </view>

      <!-- 附件信息 -->
      <view>
        <imageUpload :prohibitModification="prohibitModification" />
      </view>

      <!-- 底部操作栏 -->
      <view class="operation" v-if="purchaseGoodsInfo.in_status !== 'completed'">
        <u-button type="error" text="删除" @click="delPurchaseGoods"></u-button>
        <u-button type="success" text="暂存" @click="save"></u-button>
        <u-button type="primary" text="提交" @click="savePrompt"></u-button>
      </view>
    </scroll-view>
    <!-- 语音输入组件 -->
    <view class="input-area">
      <Input style="height: 100%" :isShowCamera="true" />
    </view>

    <!-- 交互组件 -->
    <interactive :isShowAddBtn="false" :jumpToId="2" />

    <u-modal :show="istoExamineShow" :zoom="istoExamineShowZoom" :title="toExamineTitle" :content="toExamineContent"
      :closeOnClickOverlay="true" :showCancelButton="true" :showConfirmButton="true" @confirm="save(1)" @cancel="cancel"
      @close="cancel"></u-modal>

    <!-- 搜索选择器 -->
    <searchSelector v-model:selectorShow="selectorShow" :list="selectList" :selectType="selectType"
      @confirm="selectSupplier" @close="closePopup" />

    <!-- 商品详细信息弹出层 -->
    <productDetails :productDetailsShow="productDetailsShow" :type="productDetailType" :productData="productData"
      :isShowDelBtn="true" :isShowUnit="false" @delBtnPricing="handleDelBtnPricing" @close="closeProductDetails"
      @confirm="handleProductDetails" />

    <!-- 其他费用弹出层 -->
    <otherExpenses :otherExpensesShow="otherExpensesShow" :otherExpensesContent="purchaseGoodsInfo.allocated_cost"
      :otherExpensesPay="otherExpensesPay" :prohibitModification="prohibitModification" @close="closeOtherCosts"
      @confirm="handleExpensesConfirm" />

    <!-- 结算账户弹出层 -->
    <settlementAccount :accountSelectShow="accountSelectShow" @close="closeAccountSelector" @confirm="handleAccount" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import imageUpload from "@/components/imageUpload/imageUpload.vue";
import Input from "@/components/input/input.vue";
import interactive from "@/components/interactive.vue";
import searchSelector from "@/components/searchSelector.vue";
import otherExpenses from "../components/otherExpenses.vue";
import productDetails from "@/components/productDetails.vue";
import settlementAccount from "@/components/settlementAccount/settlementAccount.vue";
import inventorySelection from "@/components/inventorySelection/inventorySelection.vue";
import MerchBill from "@/components/merchbill.vue";
import basicInfo from "@/components/modifyComponent/basicInfo/basicInfo.vue";
import { getSupplier } from "@/api/supplier";
import {
  savePurchaseGoods,
  getPurchaseGoodsDetail,
  updatePurchaseGoods,
  deletePurchaseGoods,
} from "@/api/purchaseGoods";
import { getGoodsDetail } from "@/api/goods";
import { getPurchaseordersDetail } from "@/api/purchaseOrder";

import eventBus from "@/utils/eventBus";

const uForm = ref(null);

const isLoading = ref(false); // 加载状态
const accountSelectShow = ref(false); //结算账户弹出层
const productDetailsShow = ref(false); //商品详细信息弹出层
const productDetailType = ref(0); //商品详细信息类型 0 关联采购订单物品 1 任意物品
const otherExpensesShow = ref(false); //是否弹出其他费用弹出层
const inventorySelectionShow = ref(false); // 仓库选择弹出层显示状态
const istoExamineShow = ref(false); //是否弹出审核确认框
const istoExamineShowZoom = ref(false); //审核确认框是否缩放
const toExamineTitle = ref("温馨提示"); //审核确认框标题
const toExamineContent = ref("提交后后续将不可以撤回，是否确认保存并审核？"); //审核确认框内容
const isExpandGoodsList = ref(true);
const activeQtyIndex = ref(-1);
const otherExpensesPay = reactive({
  allocated_account_name: '',
  allocated_pay_amount: 0,
  allocated_payment_method: ''
});//其他费用支付数据
const purchaseGoodsInfo = reactive<any>({
  is_commit: "", //是否提交,传1为提交，其他任何值都为暂存
  in_type: "purchase_in", //采购入库
  supplier_name: "", // 供应商name
  supplier: "", // 供应商id
  in_date: "", // 单据日期
  purchase_order: "", // 关联订单id
  purchase_order_name: "", //关联订单编号
  warehouse: "", // 仓库
  warehouse_name: "", //仓库名称
  items: [],
  total_amount: "0", // 合计
  total_cost: 0, //总成本
  discountRate: "0", // 优惠率(%)
  discount: "0", // 优惠金额
  actual_purchase_price: "0", // 优惠后金额
  other_fee: "0", // 其他费用
  payment_method: "", // 结算账户
  payment_method_name: "", // 结算账户
  deduct_deposit: "0", // 扣除预付款
  pay_amount: "0", // 本次付款
  debt: 0, //欠款
  allocated_cost: {}, //分摊金额
  allocated_pay_amount: 0, //分摊金额付款
  allocated_payment_method: "", //分摊金额支付方式
  remark: "", // 备注
});
const rules = reactive<any>({
  supplier_name: {
    type: "string",
    required: true,
    message: "请选择供应商",
    trigger: ["blur", "change"],
  },
  warehouse_name: {
    type: "string",
    required: true,
    message: "请选择仓库",
    trigger: ["blur", "change"],
  },
  payment_method: {
    type: "string",
    required: true,
    message: "请选择结算账户",
    trigger: ["blur", "change"],
  },
});
const relatedOrdersGoods = ref({}); //关联订单数据
const selectorShow = ref(false);
const selectList = ref<any[]>([]);
const selectType = ref(0); // 0 供应商 1 结算账户 2 仓库 3 关联订单
const isSupplierMore = ref(true);
const supplierPageParams = reactive({
  page: 1,
  page_size: 20,
});
const productData = ref({}); //物品信息
const prohibitModification = ref(false);//是否禁止修改

watch(() => purchaseGoodsInfo.discountRate, (newVal) => {
  if (newVal > 100) {
    uni.showToast({ title: '优惠率不能大于100', icon: "none" });
    purchaseGoodsInfo.discountRate = 100;
    return;
  }
});

onLoad((options: any) => {
  console.log(options);
  let data = {};
  if (options.data && typeof options.data === 'string') {
    try {
      data = JSON.parse(options.data);
    } catch (e) {
      console.error("解析 options.data 失败:", e);
    }
  }
  console.log(data);

  if (data.id) {
    console.log(data.id);

    getPurchaseGoodsDetail(data.id)
      .then((res: any) => {
        isLoading.value = true; // 开始加载
        console.log(res);
        if (res.code == 0) {
          Object.assign(purchaseGoodsInfo, res.data);
          getPurchaseOrderItems();
          isLoading.value = false;
          otherExpensesPay.allocated_account_name = res.data.allocated_account_name;
          otherExpensesPay.allocated_pay_amount = res.data.allocated_pay_amount;
          otherExpensesPay.allocated_payment_method = res.data.allocated_payment_method;

          console.log(otherExpensesPay);

          purchaseGoodsInfo.payment_method_name = res.data.payment_account_name;

          purchaseGoodsInfo.discount = res.data.discount;
          purchaseGoodsInfo.actual_purchase_price = res.data.total_cost;
          purchaseGoodsInfo.total_amount = Number(res.data.discount) + Number(res.data.total_cost);
          purchaseGoodsInfo.discountRate = (Number(res.data.discount) / Number(res.data.total_cost)) * 100;

          // 计算本次欠款
          purchaseGoodsInfo.debt = (
            Number(purchaseGoodsInfo.actual_purchase_price) - Number(purchaseGoodsInfo.pay_amount)
          ).toFixed(2);

          //如果状态是已完成，就禁止修改
          if (res.data.in_status === 'completed') {
            prohibitModification.value = true;
          }
        } else {
          uni.showToast({
            title: res.msg || "请求失败,请稍后再试",
            icon: "none",
          });

          isLoading.value = false;

          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        }
      })
      .catch(() => {
        isLoading.value = false; // 网络错误时结束加载
        uni.showToast({
          title: "网络问题，稍后再试",
          icon: "none",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      });
  } else {
    isLoading.value = false; // 如果没有id，不加载数据，直接结束加载
  }

  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  purchaseGoodsInfo.in_date = `${year}-${month}-${day}`;
});

onMounted(() => {
  eventBus.$on("selectOrder", (data: any) => {
    console.log("接收到选择的订单数据:", data);
    purchaseGoodsInfo.purchase_order = data.id;
    purchaseGoodsInfo.purchase_order_name = data.order_id;
    purchaseGoodsInfo.supplier = data.supplier;
    purchaseGoodsInfo.supplier_name = data.supplier_name;
    relatedOrdersGoods.value = data.items;
    // .filter(
    //   (item) => !item.purchase_order_item
    // );
    // calcTotalAndDiscount();
    console.log(relatedOrdersGoods.value);

    // purchaseGoodsInfo.items = [];
  });
  eventBus.$on("selectGoodsList", (data: any) => {
    console.log("接收到选择的商品数据:", data);
    console.log("原有的商品数据:", purchaseGoodsInfo.items);

    // 遍历传入的商品数据
    data.forEach((newItem: any) => {
      // 查找现有商品中是否有相同item和unit的商品
      const existingItemIndex = purchaseGoodsInfo.items.findIndex(
        (existingItem: any) => existingItem.item === newItem.item && existingItem.unit === newItem.unit
      );

      if (existingItemIndex !== -1) {
        // 找到相同的商品，进行覆盖但保护purchase_order_item
        const existingItem = purchaseGoodsInfo.items[existingItemIndex];
        const originalPurchaseOrderItem = existingItem.purchase_order_item;

        // 覆盖现有商品数据
        Object.assign(existingItem, newItem);

        // 如果原来有purchase_order_item，保持不变
        if (originalPurchaseOrderItem) {
          existingItem.purchase_order_item = originalPurchaseOrderItem;
        }
      } else {
        // 没有找到相同的商品，直接添加
        purchaseGoodsInfo.items.push(newItem);
      }
    });

    // 强制更新数组引用
    purchaseGoodsInfo.items = [...purchaseGoodsInfo.items];
    // calcTotalAndDiscount();
  });
});

onBeforeUnmount(() => {
  eventBus.$off("selectOrder");
  eventBus.$off("selectGoodsList");
});

// 调用采购订单接口，获取采购订单的物品
const getPurchaseOrderItems = () => {
  getPurchaseordersDetail(purchaseGoodsInfo.purchase_order)
    .then((res: any) => {
      if (res.code == 0) {
        relatedOrdersGoods.value = res.data.items;
      } else {
        uni.showToast({
          title: res.msg || "请求失败,请稍后再试",
          icon: "none",
        });
      }
    })
    .catch(() => {
      uni.showToast({
        title: "网络问题，稍后再试",
        icon: "none",
      });
    });
};

//是否禁止修改
const isProhibitModification = () => {
  console.log(prohibitModification.value);

  if (prohibitModification.value) {
    console.log("禁止修改");
    uni.showToast({
      title: "当前为已提交状态，不允许修改",
      icon: "none"
    });
    return true; // 返回true表示被禁止了
  }
  return false; // 返回false表示允许继续
};

//打开结算账户选择
const openAccountSelector = () => {
  if (isProhibitModification()) return;
  accountSelectShow.value = true;
};
//处理结算账户的数据
const handleAccount = (data: any) => {
  purchaseGoodsInfo.payment_method = data.id;
  purchaseGoodsInfo.payment_method_name = data.account_name;
};
//关闭结算账户选择
const closeAccountSelector = () => {
  accountSelectShow.value = false;
};
//处理接收其他费用数据
const handleExpensesConfirm = (data: any) => {
  purchaseGoodsInfo.allocated_cost = data;
  purchaseGoodsInfo.allocated_pay_amount = data.allocated_pay_amount;
  purchaseGoodsInfo.allocated_payment_method = data.allocated_payment_method;
};
// 打开关联订单选择
const openRelatedOrders = () => {
  if (isProhibitModification()) return;
  uni.navigateTo({
    url: "/pages/purchase/purchase?isSelectOrder=true",
  });
};
//删除关联订单
const removePurchaseOrder = () => {
  if (purchaseGoodsInfo.items && Array.isArray(relatedOrdersGoods.value)) {
    purchaseGoodsInfo.items = purchaseGoodsInfo.items.filter(
      (item: any) =>
        !relatedOrdersGoods.value.some(
          (relatedGood: any) =>
            relatedGood.item === item.item &&
            relatedGood.unit === item.unit &&
            item.purchase_order_item === relatedGood.id
        )
    );
    // calcTotalAndDiscount();
  }
  purchaseGoodsInfo.purchase_order = "";
  purchaseGoodsInfo.purchase_order_name = "";
};
const removeWarehouse = () => {
  purchaseGoodsInfo.warehouse_name = "";
  purchaseGoodsInfo.warehouse = "";
};
//删除结算账户
const removePayAccount = () => {
  purchaseGoodsInfo.payment_method = "";
  purchaseGoodsInfo.payment_method_name = "";
};

//删除其他费用
const removeOtherCosts = () => {
  purchaseGoodsInfo.allocated_cost = {};
  purchaseGoodsInfo.allocated_pay_amount = '';
  purchaseGoodsInfo.allocated_payment_method = '';
};

// 选择器
const openSelector = (type: number) => {
  if (isProhibitModification()) return;

  selectType.value = type;
  selectorShow.value = true;
  // if (type === 0) {
  //   selectList.value = supplierList.value;
  // }
};
// 选择器关闭
const closePopup = () => {
  selectorShow.value = false;
};
// 选择器选择
const selectSupplier = (item: any) => {
  if (selectType.value === 0) {
    purchaseGoodsInfo.supplier_name = item.name;
    purchaseGoodsInfo.supplier = item.id;
  }
  selectorShow.value = false;
};
// 添加任意商品
const addGoods = () => {
  uni.navigateTo({
    url:
      "/components/productSelection?data=" +
      JSON.stringify(relatedOrdersGoods.value) +
      "&type=1",
  });
};
//添加关联订单的商品
const addAssociationGoods = () => {
  uni.navigateTo({
    url:
      "/components/productSelection?data=" +
      JSON.stringify(relatedOrdersGoods.value) +
      "&type=3",
  });
};
//处理商品详细提交回来的数据
const handleProductDetails = (data: any) => {
  const targetIndex = purchaseGoodsInfo.items.findIndex(
    (infoItem: any) => infoItem.item === data.id && infoItem.unit === data.unit
  );

  if (targetIndex !== -1) {
    purchaseGoodsInfo.items[targetIndex] = data;
  }
};
//商品清单删除商品
const handleDelBtnPricing = (data: any) => {
  console.log('删除商品:', data);
  console.log('删除前商品数量:', purchaseGoodsInfo.items.length);

  // 找到要删除的商品索引
  const targetIndex = purchaseGoodsInfo.items.findIndex(
    (item: any) => String(item.item) === String(data.item) && String(item.unit) === String(data.unit)
  );

  if (targetIndex !== -1) {
    // 使用 splice 删除指定索引的元素
    purchaseGoodsInfo.items.splice(targetIndex, 1);
    console.log('删除成功，剩余商品数量:', purchaseGoodsInfo.items.length);
  } else {
    console.warn('未找到要删除的商品');
    // 如果精确匹配失败，尝试模糊匹配
    const fuzzyIndex = purchaseGoodsInfo.items.findIndex(
      (item: any) => item.item == data.item || item.unit == data.unit
    );
    if (fuzzyIndex !== -1) {
      purchaseGoodsInfo.items.splice(fuzzyIndex, 1);
    }
  }

  productDetailsShow.value = false;
};
// 弹出提示框
const savePrompt = () => {
  istoExamineShow.value = true;
};
const cancel = () => {
  istoExamineShow.value = false;
};
//暂存/提交
const save = (id: number) => {
  console.log(purchaseGoodsInfo);
  console.log(purchaseGoodsInfo.supplier);

  if (purchaseGoodsInfo.supplier == "") {
    uni.showToast({
      title: "请选择供应商",
      icon: "none",
    });
    return;
  }
  if (purchaseGoodsInfo.warehouse == "") {
    uni.showToast({
      title: "请选择仓库",
      icon: "none",
    });
    return;
  }
  if (
    Number(purchaseGoodsInfo.pay_amount) > 0 &&
    purchaseGoodsInfo.payment_method == ""
  ) {
    uni.showToast({
      title: "请选择结算账户",
      icon: "none",
    });
    return;
  }
  purchaseGoodsInfo.is_commit = id;
  // 根据是否有id判断调用新增或修改接口
  const apiMethod = purchaseGoodsInfo.id
    ? updatePurchaseGoods
    : savePurchaseGoods;
  apiMethod(purchaseGoodsInfo).then((res: any) => {
    console.log(res);
    if (res.code == 0) {
      uni.showToast({
        title: purchaseGoodsInfo.is_commit ? "提交成功" : "暂存成功",
        icon: "success",
      });
      istoExamineShow.value = false;
      uni.navigateBack({
        delta: 1,
      });
    } else {
      uni.showToast({
        title: res.msg,
        icon: "none",
      });
    }
  });
};
//删除
const delPurchaseGoods = () => {
  deletePurchaseGoods(purchaseGoodsInfo.id).then((res: any) => {
    console.log(res);
    if (res.code == 0) {
      uni.showToast({ title: "删除成功", icon: "success" });
      uni.navigateBack({
        delta: 1,
      });
    }
  });
};

// 处理金额变化
const handleAmountChange = (amountData: any) => {
  purchaseGoodsInfo.total_amount = amountData.totalAmount;
  purchaseGoodsInfo.discount = amountData.discount;
  purchaseGoodsInfo.actual_purchase_price = amountData.actualAmount;
  // 重新计算付款和欠款
  // 4. 欠款和付款金额逻辑
  if (Number(purchaseGoodsInfo.pay_amount) === 0) {
    console.log('付款金额为0');

    purchaseGoodsInfo.debt =
      Number(amountData.actualAmount);
  } else {
    console.log('付款金额不为0');
    console.log(purchaseGoodsInfo.pay_amount);
    purchaseGoodsInfo.debt =
      (
        Number(amountData.actualAmount) -
        Number(purchaseGoodsInfo.pay_amount)
      ).toFixed(2);
  }
  console.log(purchaseGoodsInfo.debt);
  console.log(amountData);

};
//计算付款变化时
const changePayAmount = (newVal: any) => {
  console.log(newVal);

  purchaseGoodsInfo.debt = (
    Number(purchaseGoodsInfo.actual_purchase_price) - Number(newVal)
  ).toFixed(2);
};
//计算欠款变化时
const changeDebt = (newVal: any) => {
  purchaseGoodsInfo.pay_amount = (
    Number(purchaseGoodsInfo.actual_purchase_price) - Number(newVal)
  ).toFixed(2);
};

// 关闭商品详情弹出层
const closeProductDetails = () => {
  productDetailsShow.value = false;
};
// 打开商品详情弹出层
const openProductDetails = (item: any) => {
  if (isProhibitModification()) return;

  if (item.purchase_order_item) {
    productDetailType.value = 0;
  } else {
    productDetailType.value = 1;
  }
  getGoodsDetail(item.item).then((res: any) => {
    if (res.code == 0) {
      productData.value = { ...item, ...res.data };
      console.log(productData.value);
      productData.value.unit = item.unit;
      productDetailsShow.value = true;
    }
  });
};

// 打开其他费用弹出层
const openOtherCosts = () => {
  otherExpensesShow.value = true;
};
// 关闭其他费用弹出层
const closeOtherCosts = () => {
  otherExpensesShow.value = false;
};
// 打开库存选择弹出层
const openInventorySelection = () => {
  if (isProhibitModification()) return;
  inventorySelectionShow.value = true;
};
// 关闭库存选择弹出层
const closeInventorySelection = () => {
  inventorySelectionShow.value = false;
};
// 接收库存弹出层的数据
const confirmInventory = (data: any) => {
  purchaseGoodsInfo.warehouse = data.id;
  purchaseGoodsInfo.warehouse_name = data.name;
};

</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  overflow: auto;
}

.info {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;

  .sub-title {
    font-size: 24rpx;
    color: #999;
  }

  .arrow {
    transition: transform 0.3s;

    &.arrow-up {
      transform: rotate(180deg);
    }
  }
}

.form {
  padding: 0 20rpx;
}

.goods-list {}

.goods_item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 20rpx;
  padding: 0 0 0 0;
}

.goods_item_click {
  display: flex;
  align-items: center;
  width: 100%;
  height: 80px;
  padding: 10px 0;
  position: relative;
}

.goods_img {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.goods_img image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods_main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_id {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}


.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.qty_x {
  font-size: 15px;
  font-weight: bold;
  margin-right: 2px;
}

.qty_control {
  display: flex;
  align-items: center;
  position: relative;
}

.qty_input {
  width: 40px;
  height: 22px;
  border: 1px solid #222;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
  background: #fff;
  color: #222;
  margin-left: 2px;
}



::v-deep .addGoods .u-button--normal.data-v-3bf2dba7 {
  padding: 0 5px;
  font-size: 12px;
}

::v-deep .addGoods .u-button.data-v-3bf2dba7 {
  height: 25px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

.arrow {
  font-size: 18px;
  color: #888;
  margin-left: 10px;
}

.total-info {
  font-size: 28rpx;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;
  margin-left: 30rpx;
  color: #303133;

  text:nth-child(1) {
    width: 80px;
    display: inline-block;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines;
  }
}

.otherExpenses {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;

}

.total-row-title {
  width: 80px;
  font-size: 28rpx;
  margin-left: 30rpx;
  color: #303133;
}

.remark {
  padding: 20rpx;
}

.upload-area {
  padding: 20rpx;
}

.add-icon {
  padding: 10rpx;
  cursor: pointer;
}

.telescoping {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40rpx;
  background-color: #fff;
}



.operation {
  width: 90%;
  height: 80rpx;
  margin: 10rpx auto;
  padding: 10rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.other-expenses-row {
  display: flex;
  align-items: center;
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

::v-deep .operation .u-button {
  height: 30px;
  width: 25%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}

::v-deep .u-input--radius.data-v-fdbb9fe6,
.u-input--square.data-v-fdbb9fe6 {
  border-radius: 4px;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
}

::v-deep .u-input__content__field-wrapper__field.data-v-fdbb9fe6 {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
  width: 125rpx;
}

::v-deep .u-form-item__body__left__content__required.data-v-5e7216f1 {
  position: absolute;
  left: 0px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}

::v-deep .u-form-item__body__left__content__label.data-v-5e7216f1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  margin-left: 30rpx;
}
</style>


/**
 * 更新purchaseGoodsInfo对象
 * @param {Object} value - 新的值
 */
<!-- const updatePurchaseGoodsInfo = (value) => {
  Object.assign(purchaseGoodsInfo, value);
};

::v-deep .u-form-item__body__left__content__label.data-v-5e7216f1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  margin-left: 30rpx;
}
</style> -->
