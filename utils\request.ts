import Fly from "flyio/dist/npm/wx.js";
import { config } from "@/config";

// 定义请求和响应的基本类型
interface FlyRequest {
  url: string;
  method?: string;
  headers: Record<string, string>;
  body?: any;
  _retry?: boolean;
  [key: string]: any;
}

interface FlyResponse {
  data: any;
  status: number;
  statusText?: string;
  headers?: Record<string, string>;
}

interface FlyError {
  request: FlyRequest;
  response?: {
    status: number;
    data: {
      detail?: string;
      msg?: string;
    };
  };
}

// 假设 app.json 是一个包含 serverIndex 和 serverUrls 的对象
// 在实际项目中，你可能需要配置 tsconfig.json 来支持 JSON 模块导入，
// 或者通过其他方式（如全局变量）获取 app.json 的内容。
//declare const config: { serverIndex: number; serverUrls: string[] };

let fly = new Fly();

/**
 * 获取服务器URL
 * @returns {string}
 */
function getServerUrl(): string {
  let index = config.serverIndex - 1;
  if (index < 0 || index >= config.serverUrls.length) {
    index = 0;
  }
  return config.serverUrls[index];
}

// 全局配置
fly.config = {
  baseURL: getServerUrl(), // 使用 app.json 文件中的配置
  timeout: 6000,
  headers: {
    'Content-Type': 'application/json; charset=UTF-8',
    'Accept': 'application/json; charset=UTF-8',
  },
};

/**
 * 请求拦截器
 */
fly.interceptors.request.use(
  (request: FlyRequest) => {
    // 自动携带Token
    const token = uni.getStorageSync('server_access_token');

    if (token) {
      request.headers['Authorization'] = `Bearer ${token}`;
    }

    // 处理非JSON数据（如FormData）
    if (request.headers['Content-Type']?.includes('multipart/form-data')) {
      request.body = request.data; // Flyio会自动处理
      delete request.data;
    }

    return request;
  },
  (error: Error) => {
    uni.showToast({ title: '请求发送失败', icon: 'none' });
    return Promise.reject(error);
  }
);

// 响应拦截器
fly.interceptors.response.use(
  (response: FlyResponse) => {
    const { data, status } = response;

    // 处理所有2xx的状态码
    if (status >= 200 && status < 300) {
      return data;
    }

    // 非2xx状态码的处理
    const errMsg = data?.msg || '请求失败';
    return Promise.reject(new Error(errMsg));
  },
  async (error: FlyError) => {
    if (error.response && error.response.status === 401) {
      // 检查是否已经重试过，如果是则直接跳转登录
      if (error.request._retry) {
        uni.showToast({ title: '登录已过期，请重新登录', icon: 'none' });
        uni.navigateTo({ url: '/pages/login/login' });
        return Promise.reject(error);
      }

      // 检测401的报错信息，分成不同情况做处理
      switch (error.response.data.detail) {
        case 'not_bind_mobile':
          uni.showToast({
            title: '未绑定手机号',
            icon: 'none',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                uni.navigateTo({ url: '/pages/order/order' });
              }, 1500);
            },
          });
          return Promise.reject(error);

        default:
          try {
            // 调用刷新令牌的函数
            const newToken = await refreshToken();
            // 从本地存储获取新的token并更新请求头
            const token = uni.getStorageSync('server_access_token');
            error.request.headers['Authorization'] = `Bearer ${token}`;
            // 标记已重试并重新发送请求
            error.request._retry = true;
            return fly.request(error.request);
          } catch (refreshError) {
            uni.showToast({ title: '登录已过期，请重新登录', icon: 'none' });
            uni.navigateTo({ url: '/pages/login/login' });
            return Promise.reject(refreshError);
          }
      }
    } else {
      let errMsg = '网络异常，请稍后重试';
      uni.showToast({ title: errMsg, icon: 'none' });
      return Promise.reject(error);
    }
  }
);

/**
 * 刷新令牌
 * @returns {Promise<string>}
 */
async function refreshToken(): Promise<string> {
  // 获取本地存储的refreshToken
  const refreshToken = uni.getStorageSync('server_refresh_token');
  // 判断是否有refreshToken
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  const response = await uni.request({
    url: `${fly.config.baseURL}/api/token/refresh/`, // 替换为后端刷新令牌的接口地址
    method: 'POST',
    data: {
      refresh_token: refreshToken,
    },
  });

  if (response.statusCode === 200 && (response.data as any).token) {
    const { token } = response.data as { token: string };
    uni.setStorageSync('server_access_token', token); // 更新本地存储的access_token
    return token;
  } else {
    throw new Error('Failed to refresh token');
  }
}

export default fly;
